package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os/exec"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/pion/webrtc/v3"
	"gopkg.in/hraban/opus.v2"
)

type SDPMessage struct {
	Type string `json:"type"`
	SDP  string `json:"sdp"`
}

type AudioProcessor struct {
	ffmpegCmd   *exec.Cmd
	ffmpegStdin io.WriteCloser
	isRecording bool
	mutex       sync.Mutex
	outputFile  string
	opusDecoder *opus.Decoder
}

func NewAudioProcessor(outputFile string) *AudioProcessor {
	// Opus decoder oluştur (48kHz, mono)
	decoder, err := opus.NewDecoder(48000, 1)
	if err != nil {
		log.Printf("Failed to create Opus decoder: %v", err)
		return nil
	}

	return &AudioProcessor{
		outputFile:  outputFile,
		isRecording: false,
		opusDecoder: decoder,
	}
}

func (ap *AudioProcessor) StartRecording() error {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	if ap.isRecording {
		return fmt.Errorf("recording already in progress")
	}

	// FFmpeg komutu: Opus'u PCM 8kHz 16bit mono'ya dönüştür
	ap.ffmpegCmd = exec.Command("ffmpeg",
		"-f", "s16le", // Input format: signed 16-bit little endian
		"-ar", "48000", // Input sample rate (WebRTC Opus default)
		"-ac", "1", // Input channels: mono
		"-i", "pipe:0", // Input from stdin
		"-ar", "8000", // Output sample rate: 8kHz
		"-ac", "1", // Output channels: mono
		"-f", "wav", // Output format: WAV
		"-y", // Overwrite output file
		ap.outputFile,
	)

	var err error
	ap.ffmpegStdin, err = ap.ffmpegCmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdin pipe: %v", err)
	}

	if err := ap.ffmpegCmd.Start(); err != nil {
		return fmt.Errorf("failed to start ffmpeg: %v", err)
	}

	ap.isRecording = true
	log.Printf("Started recording to %s", ap.outputFile)
	return nil
}

func (ap *AudioProcessor) WriteAudioData(data []byte) error {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	if !ap.isRecording || ap.ffmpegStdin == nil {
		return fmt.Errorf("recording not started")
	}

	_, err := ap.ffmpegStdin.Write(data)
	return err
}

func (ap *AudioProcessor) StopRecording() error {
	ap.mutex.Lock()
	defer ap.mutex.Unlock()

	if !ap.isRecording {
		return fmt.Errorf("recording not in progress")
	}

	if ap.ffmpegStdin != nil {
		ap.ffmpegStdin.Close()
	}

	if ap.ffmpegCmd != nil {
		if err := ap.ffmpegCmd.Wait(); err != nil {
			log.Printf("FFmpeg process ended with error: %v", err)
		}
	}

	ap.isRecording = false
	log.Printf("Stopped recording")
	return nil
}

// Opus'u PCM'e dönüştürme fonksiyonu
func (ap *AudioProcessor) convertOpusToPCM(opusData []byte) ([]byte, error) {
	if ap.opusDecoder == nil {
		return nil, fmt.Errorf("opus decoder not initialized")
	}

	// Opus frame'ini decode et
	// Maksimum 5760 sample (120ms at 48kHz)
	pcmBuffer := make([]int16, 5760)

	samplesDecoded, err := ap.opusDecoder.Decode(opusData, pcmBuffer)
	if err != nil {
		return nil, fmt.Errorf("failed to decode opus: %v", err)
	}

	// int16 slice'ını byte slice'ına dönüştür
	pcmBytes := make([]byte, samplesDecoded*2) // 16-bit = 2 bytes per sample
	for i := 0; i < samplesDecoded; i++ {
		// Little endian format
		pcmBytes[i*2] = byte(pcmBuffer[i] & 0xFF)
		pcmBytes[i*2+1] = byte((pcmBuffer[i] >> 8) & 0xFF)
	}

	return pcmBytes, nil
}

// WebRTC Answer SDP üreten fonksiyon
func createAnswerSDP(offerSDP string, audioProcessor *AudioProcessor) (string, error) {
	// WebRTC API konfigürasyonu
	config := webrtc.Configuration{
		ICEServers: []webrtc.ICEServer{
			{
				URLs: []string{"stun:stun.l.google.com:19302"},
			},
		},
	}

	// PeerConnection oluştur
	peerConnection, err := webrtc.NewPeerConnection(config)
	if err != nil {
		return "", fmt.Errorf("failed to create peer connection: %v", err)
	}

	// Audio track handler ekle
	peerConnection.OnTrack(func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		log.Printf("Track received: %s, PayloadType: %d", track.Codec().MimeType, track.PayloadType())

		// Ses kaydını başlat
		if err := audioProcessor.StartRecording(); err != nil {
			log.Printf("Failed to start recording: %v", err)
			return
		}

		// RTP paketlerini oku ve işle
		go func() {
			defer audioProcessor.StopRecording()

			for {
				rtpPacket, _, err := track.ReadRTP()
				if err != nil {
					if err == io.EOF {
						log.Println("Track ended")
						break
					}
					log.Printf("Error reading RTP: %v", err)
					continue
				}

				// RTP payload'ını al (Opus data)
				opusData := rtpPacket.Payload

				// Opus'u PCM'e dönüştür
				pcmData, err := audioProcessor.convertOpusToPCM(opusData)
				if err != nil {
					log.Printf("Error converting Opus to PCM: %v", err)
					continue
				}

				// PCM verisini FFmpeg'e gönder
				if err := audioProcessor.WriteAudioData(pcmData); err != nil {
					log.Printf("Error writing audio data: %v", err)
					continue
				}
			}
		}()
	})

	// ICE connection state değişikliklerini izle
	peerConnection.OnICEConnectionStateChange(func(connectionState webrtc.ICEConnectionState) {
		log.Printf("ICE Connection State has changed: %s", connectionState.String())
	})

	// Offer SDP'yi parse et ve set et
	offer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeOffer,
		SDP:  offerSDP,
	}

	if err := peerConnection.SetRemoteDescription(offer); err != nil {
		return "", fmt.Errorf("failed to set remote description: %v", err)
	}

	// Answer oluştur
	answer, err := peerConnection.CreateAnswer(nil)
	if err != nil {
		return "", fmt.Errorf("failed to create answer: %v", err)
	}

	// Local description'ı set et
	if err := peerConnection.SetLocalDescription(answer); err != nil {
		return "", fmt.Errorf("failed to set local description: %v", err)
	}

	log.Printf("\n---\nLocalDescription SDP created: \n%s", peerConnection.LocalDescription().SDP)
	log.Printf("\n---\nanswer SDP created: \n%s", answer.SDP)

	return answer.SDP, nil
}

func handleOffer(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	var sdpMessage SDPMessage
	if err := json.NewDecoder(r.Body).Decode(&sdpMessage); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	log.Printf("Received offer SDP: %s", sdpMessage.Type)

	// Audio processor oluştur
	timestamp := time.Now().Format("20060102_150405")
	outputFile := fmt.Sprintf("recorded_audio_%s.wav", timestamp)
	audioProcessor := NewAudioProcessor(outputFile)

	// Answer SDP oluştur
	answerSDP, err := createAnswerSDP(sdpMessage.SDP, audioProcessor)
	if err != nil {
		log.Printf("Error creating answer SDP: %v", err)
		http.Error(w, "Failed to create answer", http.StatusInternalServerError)
		return
	}

	response := SDPMessage{
		Type: "answer",
		SDP:  answerSDP,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	log.Printf("Sent answer SDP")
}

func main() {
	r := mux.NewRouter()

	// WebRTC signaling endpoint
	r.HandleFunc("/offer", handleOffer).Methods("POST", "OPTIONS")

	// Static file server for web page
	r.PathPrefix("/").Handler(http.FileServer(http.Dir("./static/")))

	log.Println("Server starting on :8080")
	log.Fatal(http.ListenAndServe(":8080", r))
}

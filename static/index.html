<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Ses Kaydı Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>WebRTC Ses Kaydı Test</h1>
    
    <div>
        <button id="startBtn">Ses <PERSON></button>
        <button id="stopBtn" disabled>Ses <PERSON></button>
    </div>
    
    <div id="status" class="status info">
        Hazır. "Ses Kaydını Başlat" butonuna tıklayın.
    </div>

    <script>
        let localStream = null;
        let peerConnection = null;
        let isRecording = false;

        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusDiv = document.getElementById('status');

        function updateStatus(message, type = 'info') {
            statusDiv.textContent = statusDiv.textContent + "\n" + message;
            statusDiv.className = `status ${type}`;
        }

        async function startRecording() {
            try {
                updateStatus('Mikrofon erişimi isteniyor...', 'info');
                
                // Mikrofon erişimi al
                localStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 48000,
                        channelCount: 1
                    },
                    video: false
                });

                updateStatus('WebRTC bağlantısı kuruluyor...', 'info');

                // PeerConnection oluştur
                peerConnection = new RTCPeerConnection({
                    iceServers: [
                        {
                            urls: [
                                'stun:stun.l.google.com:19302',
                                'stun:stun1.l.google.com:19302',
                                'stun:stun2.l.google.com:19302'
                            ]
                        }
                    ]
                });

                // pc.addTransceiver("video", {'direction': 'recvonly'})
                peerConnection.addTransceiver("audio", {'direction': 'sendonly'})                

                // Audio track ekle
                localStream.getAudioTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                    console.log("Audio track added: " + track.id);
                });

                // ICE candidate event handler
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        console.log('ICE candidate:', event.candidate);
                    }
                };

                // Connection state change handler
                peerConnection.onconnectionstatechange = () => {
                    console.log('Connection state:', peerConnection.connectionState);
                    updateStatus(`Bağlantı durumu: ${peerConnection.connectionState}`, 'info');
                };

                // Offer oluştur
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);

                updateStatus('Sunucuya offer gönderiliyor...', 'info');

                // Offer'ı sunucuya gönder
                const response = await fetch('/offer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'offer',
                        sdp: offer.sdp
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const answerData = await response.json();
                console.log("Answer SDP received: " + answerData.sdp);
                
                // Answer'ı set et
                await peerConnection.setRemoteDescription({
                    type: 'answer',
                    sdp: answerData.sdp
                });

                updateStatus('Ses kaydı başlatıldı! Konuşmaya başlayabilirsiniz.', 'success');
                isRecording = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;

            } catch (error) {
                console.error('Error starting recording:', error);
                updateStatus(`Hata: ${error.message}`, 'error');
                cleanup();
            }
        }

        function stopRecording() {
            updateStatus('Ses kaydı durduruluyor...', 'info');
            cleanup();
            updateStatus('Ses kaydı durduruldu.', 'info');
        }

        function cleanup() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            isRecording = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }

        startBtn.addEventListener('click', startRecording);
        stopBtn.addEventListener('click', stopRecording);

        // Sayfa kapatılırken cleanup yap
        window.addEventListener('beforeunload', cleanup);
    </script>
</body>
</html>
